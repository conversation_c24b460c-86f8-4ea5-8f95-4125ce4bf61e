# Copilot Instructions for This Codebase

## 项目架构与模块说明

- 本仓库包含多个独立但相关的模块，主要聚焦于足球队管理、AI辅助信息收集、Word文档自动生成等。
- 主要目录：
  - `streamlit_team_management_modular/`：基于Streamlit的球队管理系统，采用模块化设计，包含数据模型、服务、配置、数据访问等分层结构。
  - `word_zc/ai-football-generator/`：专注于Word报名表自动生成，强调Python集成与模板引擎（poi-tl）适配。
  - `streamlit_team_management_modular/poi-tl/`：Word模板引擎（Java实现），支持丰富的Word模板渲染能力。

## 关键开发模式与约定

- **模块化分层**：每个子系统均采用分层结构（如 models, data, services, config），便于维护和扩展。
- **数据驱动**：数据模型清晰，所有业务逻辑围绕数据结构展开，数据校验和转换有专门实现。
- **异常与日志**：各层有完善的异常处理和日志输出，便于定位问题。
- **Python-Java集成**：Word生成核心通过JPype等方式与Java模板引擎集成，相关适配器位于`PythonIntegrationAdapter.java`等文件。
- **批量与自动化**：支持批量上传、批量处理、自动化信息收集等，相关脚本和入口在根目录和各模块下。

## 典型工作流

- **本地运行/调试**：
  - 进入`streamlit_team_management_modular/`，运行`streamlit run app.py`启动主应用。
  - 依赖管理见`requirements.txt`，如需Java支持需确保poi-tl相关依赖可用。
- **Word生成**：
  - 通过`ai-football-generator`模块或相关Python脚本调用Java模板引擎生成Word文档。
  - 模板与数据分离，模板文件位于专门目录，数据通过模型/脚本传递。
- **测试与验证**：
  - 测试脚本以`test_*.py`命名，位于根目录，直接运行即可。
  - 典型命令：`python test_xxx.py`。

## 项目特有约定

- **命名规范**：所有批量/自动化脚本以`test_`或`verify_`开头，便于批量测试和验证。
- **图片与文档处理**：图片处理、背景移除等功能通过AI模型和脚本实现，相关文件以`api_test_`、`test_bg_removal_`等命名。
- **配置与常量**：所有可配置项集中在`config/`和`constants.py`，便于统一管理。

## 重要文件/目录参考

- `streamlit_team_management_modular/app.py`：主入口
- `streamlit_team_management_modular/models/`：数据模型
- `streamlit_team_management_modular/data/`：数据访问
- `streamlit_team_management_modular/services/`：业务逻辑
- `word_zc/ai-football-generator/`：Word生成核心
- `test_*.py`、`verify_*.py`：批量测试/验证脚本

## 其他说明

- Java与Python集成细节见`ai-football-generator`和`poi-tl`相关文档。
- 代码风格以清晰、可维护为主，优先遵循现有分层和命名约定。

如需补充或有疑问，请在下方补充说明。
