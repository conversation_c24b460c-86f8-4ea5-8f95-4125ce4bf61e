#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队徽数据流 - 验证当前_prepare_json_data方法的问题
"""

import os
import json

def simulate_current_prepare_json_data(team_data, players_data):
    """模拟当前的_prepare_json_data逻辑"""
    team_name = team_data.get('name', '足球队')
    
    team_info = {
        "title": f"{team_name}报名表",
        "organizationName": team_name,
        "teamLeader": team_data.get('leader', ''),
        "coach": team_data.get('coach', ''),
        "teamDoctor": team_data.get('doctor', ''),
        "contactPerson": team_data.get('contact_person', team_data.get('leader', '')),
        "contactPhone": team_data.get('contact_phone', ''),
        "jerseyColor": team_data.get('jersey_color', ''),
        "shortsColor": team_data.get('shorts_color', ''),
        "socksColor": team_data.get('socks_color', ''),
        "goalkeeperKitColor": team_data.get('goalkeeper_kit_color', '')
    }
    
    players = []
    for player in players_data:
        if player.get('name') and player.get('jersey_number'):
            photo_path = player.get('photo', '')
            players.append({
                "number": str(player.get('jersey_number', '')),
                "name": player.get('name', ''),
                "photoPath": photo_path
            })
    
    return {
        "teamInfo": team_info,
        "players": players,
        "config": {"templatePath": "template.docx"},
        "metadata": {"team_name": team_name}
    }

def simulate_fixed_prepare_json_data(team_data, players_data):
    """模拟修复后的_prepare_json_data逻辑"""
    team_name = team_data.get('name', '足球队')
    
    team_info = {
        "title": f"{team_name}报名表",
        "organizationName": team_name,
        "teamLeader": team_data.get('leader', ''),
        "coach": team_data.get('coach', ''),
        "teamDoctor": team_data.get('doctor', ''),
        "contactPerson": team_data.get('contact_person', team_data.get('leader', '')),
        "contactPhone": team_data.get('contact_phone', ''),
        "jerseyColor": team_data.get('jersey_color', ''),
        "shortsColor": team_data.get('shorts_color', ''),
        "socksColor": team_data.get('socks_color', ''),
        "goalkeeperKitColor": team_data.get('goalkeeper_kit_color', '')
    }
    
    players = []
    for player in players_data:
        if player.get('name') and player.get('jersey_number'):
            photo_path = player.get('photo', '')
            players.append({
                "number": str(player.get('jersey_number', '')),
                "name": player.get('name', ''),
                "photoPath": photo_path
            })
    
    # 🔑 关键修复：添加队徽处理
    logo_path = team_data.get('logo_path')
    if logo_path and os.path.exists(logo_path):
        logo_path = os.path.abspath(logo_path)
    
    return {
        "teamInfo": team_info,
        "players": players,
        "config": {"templatePath": "template.docx"},
        "logoPath": logo_path,  # 🔑 关键添加
        "metadata": {"team_name": team_name}
    }

def test_data_flow():
    """测试数据流"""
    print("=== 队徽数据流测试 ===")
    print()
    
    # 测试数据
    team_data = {
        'name': '测试队',
        'leader': '队长',
        'logo_path': '/path/to/team_logo.png'  # 关键：队徽路径
    }
    
    players_data = [
        {'name': '球员1', 'jersey_number': '1', 'photo': '/path/to/player1.jpg'}
    ]
    
    print("1. 输入数据:")
    print(f"   team_data包含logo_path: {'logo_path' in team_data}")
    print(f"   logo_path值: {team_data.get('logo_path', 'MISSING')}")
    print()
    
    print("2. 当前_prepare_json_data处理结果:")
    current_result = simulate_current_prepare_json_data(team_data, players_data)
    
    print(f"   返回数据的顶级键: {list(current_result.keys())}")
    print(f"   是否包含logo_path: {'logo_path' in current_result}")
    print(f"   是否包含logoPath: {'logoPath' in current_result}")
    print()
    
    print("3. 修复后_prepare_json_data处理结果:")
    fixed_result = simulate_fixed_prepare_json_data(team_data, players_data)
    
    print(f"   返回数据的顶级键: {list(fixed_result.keys())}")
    print(f"   是否包含logoPath: {'logoPath' in fixed_result}")
    print(f"   logoPath值: {fixed_result.get('logoPath', 'MISSING')}")
    print()
    
    print("4. 问题确认:")
    print("   ❌ 当前版本：logo_path数据丢失 - 没有传递给Java")
    print("   ❌ 当前版本：Java无法获取队徽路径")
    print("   ❌ 当前版本：{{@teamLogoPhoto}}占位符无法被替换")
    print()
    
    print("5. 修复验证:")
    print("   ✅ 修复版本：logoPath正确传递给Java")
    print("   ✅ 修复版本：Java可以获取队徽路径")
    print("   ✅ 修复版本：{{@teamLogoPhoto}}占位符可以被替换")
    print()
    
    print("6. 需要的修改:")
    print("   在_prepare_json_data的return语句中添加:")
    print('   "logoPath": team_data.get("logo_path")')

if __name__ == "__main__":
    test_data_flow()
