#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队徽问题最佳解决方案 - 代码示例
"""

# ============================================================================
# 第一步：Python端修改 (word_generator_service.py)
# ============================================================================

def _prepare_json_data_FIXED(self, team_data, players_data):
    """修复后的_prepare_json_data方法"""
    # 处理球队信息
    team_name = team_data.get('name', '足球队')
    
    team_info = {
        "title": f"{team_name}报名表",
        "organizationName": team_name,
        "teamLeader": team_data.get('leader', ''),
        "coach": team_data.get('coach', ''),
        "teamDoctor": team_data.get('doctor', ''),
        "contactPerson": team_data.get('contact_person', team_data.get('leader', '')),
        "contactPhone": team_data.get('contact_phone', ''),
        # 添加颜色字段映射
        "jerseyColor": team_data.get('jersey_color', ''),
        "shortsColor": team_data.get('shorts_color', ''),
        "socksColor": team_data.get('socks_color', ''),
        "goalkeeperKitColor": team_data.get('goalkeeper_kit_color', '')
    }
    
    # 处理球员信息
    players = []
    for player in players_data:
        if player.get('name') and player.get('jersey_number'):
            # 处理照片路径
            photo_path = player.get('photo', '')
            if photo_path and not os.path.isabs(photo_path):
                # 转换为绝对路径
                photo_path = os.path.abspath(photo_path)
            
            players.append({
                "number": str(player.get('jersey_number', '')),
                "name": player.get('name', ''),
                "photoPath": photo_path
            })
    
    # 配置信息
    config = {
        "templatePath": os.path.abspath(self.template_path),
        "outputDir": os.path.abspath(self.output_dir),
        "photosDir": os.path.abspath("photos")
    }
    
    # 🔑 关键修复：添加队徽处理
    logo_path = team_data.get('logo_path')
    if logo_path and os.path.exists(logo_path):
        # 转换为绝对路径
        logo_path = os.path.abspath(logo_path)
        print(f"✅ 队徽路径已添加到JSON数据: {logo_path}")
    else:
        logo_path = None
        print("⚠️ 未找到队徽文件或路径为空")
    
    return {
        "teamInfo": team_info,
        "players": players,
        "config": config,
        "logoPath": logo_path,  # 🔑 关键添加：队徽路径
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "python_version": "streamlit_integration",
            "team_name": team_name,
            "player_count": len(players)
        }
    }

# ============================================================================
# 第二步：Java端修改示例
# ============================================================================

# FootballTeamData.java 需要添加的代码：
JAVA_FOOTBALLTEAMDATA_ADDITION = """
public class FootballTeamData {
    private TeamInfo teamInfo;
    private PlayerData[] players;
    private String logoPath;  // 🔑 新增：队徽路径字段

    // 现有构造函数和方法...

    // 🔑 新增：队徽相关的getter和setter
    public String getLogoPath() {
        return logoPath;
    }

    public void setLogoPath(String logoPath) {
        this.logoPath = logoPath;
    }
}
"""

# JsonDataParser.java 需要添加的代码：
JAVA_JSONDATAPARSER_ADDITION = """
public static FootballTeamData parseFromJson(String jsonFilePath) {
    try {
        JsonNode rootNode = objectMapper.readTree(new File(jsonFilePath));
        
        // 解析队伍信息
        TeamInfo teamInfo = parseTeamInfo(rootNode.get("teamInfo"));
        
        // 解析球员信息
        PlayerData[] players = parsePlayers(rootNode.get("players"));
        
        // 创建完整的球队数据
        FootballTeamData teamData = FootballTeamData.fromPythonData(teamInfo, players);
        
        // 🔑 新增：解析队徽路径
        JsonNode logoPathNode = rootNode.get("logoPath");
        if (logoPathNode != null && !logoPathNode.isNull()) {
            String logoPath = logoPathNode.asText();
            teamData.setLogoPath(logoPath);
            System.out.println("✅ 队徽路径已解析: " + logoPath);
        } else {
            System.out.println("⚠️ JSON中未找到logoPath字段");
        }
        
        return teamData;
        
    } catch (Exception e) {
        System.err.println("ERROR:Failed to parse JSON data: " + e.getMessage());
        return null;
    }
}
"""

# WordGeneratorCore.java 需要添加的代码：
JAVA_WORDGENERATORCORE_ADDITION = """
private Map<String, Object> prepareTemplateData(FootballTeamData teamData) {
    Map<String, Object> data = new HashMap<>();
    
    // 现有的球队信息和球员处理代码...
    
    // 🔑 新增：队徽处理逻辑
    String logoPath = teamData.getLogoPath();
    if (logoPath != null && !logoPath.trim().isEmpty()) {
        try {
            File logoFile = new File(logoPath);
            if (logoFile.exists()) {
                data.put("teamLogoPhoto", 
                    Pictures.ofLocal(logoPath)
                        .size(120, 120)  // 设置队徽尺寸
                        .center()         // 居中对齐
                        .create());
                System.out.println("✅ 队徽已添加到模板数据: " + logoPath);
            } else {
                System.err.println("⚠️ 队徽文件不存在: " + logoPath);
            }
        } catch (Exception e) {
            System.err.println("⚠️ 队徽处理失败: " + e.getMessage());
        }
    } else {
        System.out.println("⚠️ 队徽路径为空，跳过队徽处理");
    }
    
    return data;
}
"""

# ============================================================================
# 第三步：验证测试代码
# ============================================================================

def test_complete_solution():
    """测试完整解决方案"""
    print("=== 队徽问题完整解决方案测试 ===")
    print()
    
    # 模拟测试数据
    team_data = {
        'name': '测试队',
        'leader': '队长',
        'logo_path': '/path/to/team_logo.png'
    }
    
    players_data = [
        {'name': '球员1', 'jersey_number': '1', 'photo': '/path/to/player1.jpg'}
    ]
    
    print("1. Python端数据准备:")
    print(f"   输入team_data包含logo_path: {'logo_path' in team_data}")
    
    # 模拟修复后的方法
    # result = _prepare_json_data_FIXED(None, team_data, players_data)
    
    print("2. 预期JSON结构:")
    expected_json = {
        "teamInfo": {"organizationName": "测试队"},
        "players": [{"number": "1", "name": "球员1"}],
        "config": {"templatePath": "template.docx"},
        "logoPath": "/path/to/team_logo.png",  # 关键字段
        "metadata": {"team_name": "测试队"}
    }
    
    print(f"   JSON包含logoPath: {'logoPath' in expected_json}")
    print(f"   logoPath值: {expected_json['logoPath']}")
    
    print()
    print("3. Java端处理流程:")
    print("   JsonDataParser.parseFromJson() → 读取logoPath")
    print("   FootballTeamData.setLogoPath() → 存储队徽路径")
    print("   WordGeneratorCore.prepareTemplateData() → 处理队徽")
    print("   data.put('teamLogoPhoto', Pictures.ofLocal(...)) → 添加到模板")
    
    print()
    print("4. 最终效果:")
    print("   Word模板: {{@teamLogoPhoto}} → 实际队徽图片 ✅")

if __name__ == "__main__":
    test_complete_solution()
    
    print("\n" + "="*60)
    print("📋 实施清单:")
    print("□ 1. 修改 word_generator_service.py 的 _prepare_json_data 方法")
    print("□ 2. 修改 FootballTeamData.java 添加 logoPath 字段")
    print("□ 3. 修改 JsonDataParser.java 解析 logoPath")
    print("□ 4. 修改 WordGeneratorCore.java 处理队徽")
    print("□ 5. 重新编译 Java 代码")
    print("□ 6. 运行完整测试")
    print("="*60)
